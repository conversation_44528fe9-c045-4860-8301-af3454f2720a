-- 更新用户登录记录表结构
-- 添加设备信息相关字段

-- 添加登录时间字段
ALTER TABLE `user_login_record` ADD COLUMN `login_time` datetime NULL DEFAULT NULL COMMENT '登录时间' AFTER `user_id`;

-- 添加客户端平台字段
ALTER TABLE `user_login_record` ADD COLUMN `platform` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '客户端平台' AFTER `location`;

-- 添加设备品牌字段
ALTER TABLE `user_login_record` ADD COLUMN `brand` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '设备品牌' AFTER `platform`;

-- 添加设备型号字段
ALTER TABLE `user_login_record` ADD COLUMN `model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '设备型号' AFTER `brand`;

-- 添加微信版本号字段
ALTER TABLE `user_login_record` ADD COLUMN `version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信版本号' AFTER `model`;

-- 添加客户端基础库版本字段
ALTER TABLE `user_login_record` ADD COLUMN `sdk_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '客户端基础库版本' AFTER `version`;

-- 添加是否首次登录字段
ALTER TABLE `user_login_record` ADD COLUMN `is_first_login` tinyint(1) NULL DEFAULT 0 COMMENT '是否首次登录（0否 1是）' AFTER `sdk_version`;

-- 添加设备系统版本字段
ALTER TABLE `user_login_record` ADD COLUMN `system` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '设备系统版本' AFTER `is_first_login`;

-- 添加设备平台字段
ALTER TABLE `user_login_record` ADD COLUMN `device_platform` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '设备平台' AFTER `system`;

-- 修改create_time字段默认值
ALTER TABLE `user_login_record` MODIFY COLUMN `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 添加索引
ALTER TABLE `user_login_record` ADD INDEX `idx_user_login_record_user_id`(`user_id` ASC) USING BTREE;
ALTER TABLE `user_login_record` ADD INDEX `idx_user_login_record_login_time`(`login_time` ASC) USING BTREE;

-- 更新现有记录的login_time字段（使用create_time的值）
UPDATE `user_login_record` SET `login_time` = `create_time` WHERE `login_time` IS NULL;

-- 更新现有记录的platform字段
UPDATE `user_login_record` SET `platform` = 'miniprogram' WHERE `platform` = '' OR `platform` IS NULL;
