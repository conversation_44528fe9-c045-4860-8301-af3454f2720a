<view class="container">
  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" color="#1989fa" size="24px" vertical>
    加载中...
  </van-loading>

  <!-- 主要内容 -->
  <view wx:else>
    <!-- 统计信息卡片 -->
    <view class="stats-card">
      <view class="stats-title">登录统计</view>
      <view class="stats-grid">
        <view class="stats-item">
          <view class="stats-number">{{totalLoginTimes}}</view>
          <view class="stats-label">总登录次数</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">{{recentWeekLoginTimes}}</view>
          <view class="stats-label">近7天登录</view>
        </view>
      </view>
      <view class="stats-info">
        <text class="stats-text">首次登录：{{firstLoginTime}}</text>
      </view>
    </view>

    <!-- 设备统计 -->
    <view class="device-stats" wx:if="{{Object.keys(deviceStats).length > 0}}">
      <view class="section-title">常用设备</view>
      <view class="device-list">
        <view class="device-item" wx:for="{{Object.entries(deviceStats)}}" wx:key="index">
          <view class="device-name">{{item[0]}}</view>
          <view class="device-count">{{item[1]}}次</view>
        </view>
      </view>
    </view>

    <!-- 最近登录记录 -->
    <view class="records-section">
      <view class="section-title">最近登录记录</view>
      
      <view wx:if="{{recentRecords.length === 0}}" class="empty-state">
        <text class="empty-text">暂无登录记录</text>
      </view>

      <view wx:else class="records-list">
        <view 
          class="record-item" 
          wx:for="{{recentRecords}}" 
          wx:key="id"
          data-record="{{item}}"
          bindtap="onRecordTap"
        >
          <view class="record-main">
            <view class="record-device">
              <text class="device-icon">📱</text>
              <text class="device-text">{{item.deviceInfo}}</text>
            </view>
            <view class="record-time">{{item.loginTimeFormatted}}</view>
          </view>
          
          <view class="record-details">
            <view class="record-location">
              <text class="location-icon">📍</text>
              <text class="location-text">{{item.location || '未知位置'}}</text>
            </view>
            <view class="record-type" wx:if="{{item.isFirstLogin === 1}}">
              <text class="first-login-badge">首次登录</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 刷新按钮 -->
    <view class="refresh-section">
      <van-button 
        type="default" 
        size="small" 
        icon="replay" 
        bind:click="onRefresh"
        loading="{{loading}}"
      >
        刷新数据
      </van-button>
    </view>
  </view>
</view>
