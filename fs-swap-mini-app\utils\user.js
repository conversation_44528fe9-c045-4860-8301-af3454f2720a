const api = require('../config/api')
// 引入全局配置
const globalConfig = require('../config/config')
const localResidentialService = require('../services/localResidential')

// 常量配置 - 使用全局配置
const CONFIG = {
    STORAGE_KEYS: globalConfig.STORAGE_KEYS,
    RESIDENTIAL_CERTIFICATION_STATUS: globalConfig.RESIDENTIAL_CERTIFICATION_STATUS
}

// 错误消息 - 使用全局配置
const MESSAGES = globalConfig.MESSAGES

// 错误类型
const ERROR_TYPES = {
    SESSION_EXPIRED: 'session_expired',
    NETWORK_ERROR: 'network_error',
    AUTH_DENIED: 'auth_denied',
    UNKNOWN_ERROR: 'unknown_error',
}

/**
 * Promise封装wx.checkSession
 */
function checkSession() {
    return new Promise(function (resolve, reject) {
        wx.checkSession({
            success: function () {
                resolve(true)
            },
            fail: function () {
                reject(false)
            },
        })
    })
}

/**
 * Promise封装wx.login
 */
function wxLogin() {
    return new Promise(function (resolve, reject) {
        wx.login({
            success: function (res) {
                if (res.code) {
                    resolve(res)
                } else {
                    reject(res)
                }
            },
            fail: function (err) {
                reject(err)
            },
        })
    })
}

/**
 * 获取设备信息
 * 根据微信官方文档获取设备和应用信息
 * @returns {Object} 设备信息对象
 */
function getDeviceInfo() {
    try {
        // 优先使用官方推荐的新API获取设备信息
        const deviceInfo = wx.getDeviceInfo();
        const appBaseInfo = wx.getAppBaseInfo();

        return {
            platform: deviceInfo.platform || '',
            brand: deviceInfo.brand || '',
            model: deviceInfo.model || '',
            version: appBaseInfo.version || '',
            sdkVersion: appBaseInfo.SDKVersion || ''
        };
    } catch (error) {
        console.warn('获取设备信息失败，使用默认值:', error);
        // 获取失败时直接返回默认值
        return {
            platform: 'miniprogram',
            brand: '',
            model: '',
            version: '',
            sdkVersion: ''
        };
    }
}

/**
 * 清除登录状态
 * 统一处理登出逻辑，清除token和用户信息
 */
function clearLoginState() {
    // 清除本地存储
    wx.removeStorageSync(CONFIG.STORAGE_KEYS.TOKEN)
    wx.removeStorageSync(CONFIG.STORAGE_KEYS.USER_INFO)

    // 更新全局状态
    const app = getApp()
    if (app && app.globalData) {
        app.globalData.userInfo = null
        app.setLoginStatus(false)


    }

    // 重置静默登录状态标志
    isSilentLoggingIn = false
}

/**
 * 检查登录状态
 * 验证token是否存在并有效
 * @returns {Promise<boolean>} 登录状态是否有效
 */
async function checkLoginStatus() {
    // 检查本地是否有token
    const token = wx.getStorageSync(CONFIG.STORAGE_KEYS.TOKEN)
    if (!token) {
        // token不存在，清除登录状态
        clearLoginState()
        return false
    }

    try {
        // 调用API检查token是否有效
        await api.checkLogin()

        // token有效，更新登录状态
        const app = getApp()
        if (app && app.globalData) {
            app.setLoginStatus(true)
        }
        return true
    } catch (error) {
        // token无效，清除登录状态
        clearLoginState()
        return false
    }
}

// 状态标志，防止重复操作
let isSilentLoggingIn = false // 静默登录状态标志
let silentLoginCompleted = false // 静默登录是否已完成（成功或失败）

// 静默登录超时时间（毫秒）
const SILENT_LOGIN_TIMEOUT = 10000

/**
 * 执行静默登录
 * @returns {Promise<boolean>} 登录是否成功
 */
async function silentLogin() {
    // 防止重复登录或已完成的登录
    if (isSilentLoggingIn || silentLoginCompleted) {
        return silentLoginCompleted ? getApp().safeGetGlobalData('hasLogin', false) : false
    }

    // 设置超时处理
    let loginTimeout = null
    const timeoutPromise = new Promise((_, reject) => {
        loginTimeout = setTimeout(() => {
            reject(new Error('静默登录超时'))
        }, SILENT_LOGIN_TIMEOUT)
    })

    try {
        isSilentLoggingIn = true

        // 使用Promise.race实现超时控制
        const loginPromise = (async () => {
            try {
                // 直接使用微信登录code进行静默登录
                const wxLoginResult = await wxLogin()

                // 获取设备信息
                const deviceInfo = getDeviceInfo();

                // 构建登录参数，包含本地选择的小区信息和设备信息
                const param = {
                    loginCode: wxLoginResult.code,
                    ...deviceInfo
                }

                // 如果有本地选择的小区，添加到登录参数中
                const localResidentialService = require('../services/localResidential')
                localResidentialService.init();
                const residentialId = localResidentialService.getCurrentResidentialId();
                if (residentialId) {
                    param.residentialId = residentialId;
                }

                // 调用API模块中的登录方法
                const loginResult = await api.loginByWechat(param)

                if (!loginResult || !loginResult.token) {
                    throw new Error('登录失败，未获取到token')
                }

                // 保存登录态
                wx.setStorageSync(CONFIG.STORAGE_KEYS.TOKEN, loginResult.token)

                // 获取用户信息并更新到全局状态
                await updateUserInfo()
                return true
            } catch (error) {
                throw error
            }
        })()

        // 等待登录完成或超时
        const result = await Promise.race([loginPromise, timeoutPromise])
        return result
    } catch (error) {
        // 确保清除任何可能存在的无效登录状态
        clearLoginState()
        return false
    } finally {
        // 清除超时定时器
        if (loginTimeout) {
            clearTimeout(loginTimeout)
        }
        // 无论成功失败，都重置登录状态标志
        isSilentLoggingIn = false
        silentLoginCompleted = true

        // 在所有状态设置完毕后通知静默登录完成
        const finalResult = getApp().safeGetGlobalData('hasLogin', false)
        notifySilentLoginCompleted(finalResult)
    }
}

/**
 * 通知静默登录完成
 * @param {boolean} success 登录是否成功
 */
function notifySilentLoginCompleted(success) {
    const app = getApp()
    if (app && app.notifyWatchers) {
        app.notifyWatchers('silentLoginCompleted', success, null)
    }

    console.log('静默登录完成，结果:', success)
}

/**
 * 检查静默登录是否已完成
 * @returns {boolean} 是否已完成
 */
function isSilentLoginCompleted() {
    return silentLoginCompleted
}

/**
 * 重置静默登录状态（用于测试或特殊情况）
 */
function resetSilentLoginState() {
    silentLoginCompleted = false
    isSilentLoggingIn = false
}

/**
 * 处理登录相关错误
 * @param {Error} error 错误对象
 * @returns {string} 错误类型
 */
function handleLoginError(error) {
    // 针对会话过期的特殊处理
    if (error.errMsg && error.errMsg.includes('session time out')) {
        wx.showToast({
            title: MESSAGES.SESSION_EXPIRED,
            icon: 'none',
            duration: 2000,
        })
        clearLoginState()
        return ERROR_TYPES.SESSION_EXPIRED
    }

    // 授权被拒绝
    if (error.message === 'phone_auth_denied' ||
        (error.errMsg && error.errMsg.includes('getPhoneNumber:fail'))) {
        wx.showToast({
            title: MESSAGES.PHONE_AUTH_DENIED || MESSAGES.AUTH_DENIED,
            icon: 'none',
            duration: 2000,
        })
        return ERROR_TYPES.AUTH_DENIED
    }

    // 网络错误
    if (error.errMsg && (error.errMsg.includes('request:fail') || error.errMsg.includes('timeout'))) {
        wx.showToast({
            title: MESSAGES.NETWORK_ERROR,
            icon: 'none',
            duration: 2000,
        })
        return ERROR_TYPES.NETWORK_ERROR
    }

    // 其他错误的处理
    wx.showToast({
        title: error.message || MESSAGES.ACCESS_ERROR,
        icon: 'error',
        duration: 2000,
    })
    return ERROR_TYPES.UNKNOWN_ERROR
}

/**
 * 更新用户信息 - 从服务器获取最新数据并更新到全局状态
 * @returns {Promise<Object>} 更新后的用户信息
 */
async function updateUserInfo() {
    try {
        // 获取token
        const token = wx.getStorageSync(CONFIG.STORAGE_KEYS.TOKEN);
        if (!token) {
            throw new Error('未找到登录凭证');
        }

        // 调用API模块中的获取用户信息方法
        const res = await api.loginUserInfo();

        if (res.code === 200 && res.data) {
            // 更新全局状态和本地存储
            const userData = res.data
            const app = getApp()
            app.globalData.userInfo = userData

            // 通知监听器
            app.notifyWatchers(
                'globalData.userInfo',
                app.globalData.userInfo,
                null
            )

            // 保存到本地存储
            wx.setStorageSync(
                CONFIG.STORAGE_KEYS.USER_INFO,
                app.globalData.userInfo
            )

            // 更新登录状态
            app.setLoginStatus(true)

            // 用户信息更新后，重新初始化小区状态
            // 这样可以正确获取用户绑定的小区信息
            if (app.initResidentialStatus) {
                app.initResidentialStatus()
            }

            return app.globalData.userInfo
        } else {
            clearLoginState()
            return null
        }
    } catch (error) {
        handleLoginError(error)
        clearLoginState()
        return null
    }
}

/**
 * 显示登录组件
 * @param {Object} pageContext 页面上下文
 * @param {boolean} autoClose 登录成功后是否自动关闭
 * @returns {boolean} 是否成功显示登录组件
 * @deprecated 请使用 globalComponents.showLoginComponent 替代
 */
function showLoginComponent(pageContext, autoClose = true) {
    // 使用全局组件工具
    const globalComponents = require('./globalComponents')
    return globalComponents.showLoginComponent(pageContext, autoClose)
}

/**
 * 隐藏登录组件
 * @param {Object} pageContext 页面上下文
 * @returns {boolean} 是否成功隐藏登录组件
 * @deprecated 请使用 globalComponents.hideLoginComponent 替代
 */
function hideLoginComponent(pageContext) {
    // 使用全局组件工具
    const globalComponents = require('./globalComponents')
    return globalComponents.hideLoginComponent(pageContext)
}

/**
 * @param {Object} pageContext 页面上下文对象（this）
 * @param {Object} options 配置选项
 * @param {Function} options.onLoginSuccess 登录成功回调
 * @param {Function} options.onLoginRequired 需要显示登录框时的回调
 * @param {boolean} options.allowGuestMode 是否允许游客模式，默认false
 * @param {Function} options.guestModeCallback 游客模式回调
 * @param {number} options.initialDelay 初始检查延迟时间（毫秒），默认100ms
 * @returns {Object} 返回清理方法和控制方法
 */
function initPageLogin(pageContext, options = {}) {
    const {
        onLoginSuccess,
        onLoginRequired,
        allowGuestMode = false,
        guestModeCallback,
        initialDelay = 100 // 很短的延迟，只是为了让页面初始化完成
    } = options

    const app = getApp()
    if (!app || !pageContext) {
        console.error('initPageLogin: 缺少必要的参数')
        return { cleanup: () => {} }
    }

    let hasProcessed = false // 防止重复处理

    // 登录成功处理
    function handleLoginSuccess() {
        if (onLoginSuccess) {
            onLoginSuccess()
        }
    }

    // 处理未登录状态
    function handleNotLoggedIn() {
        if (allowGuestMode && guestModeCallback) {
            // 允许游客模式，执行游客模式回调
            guestModeCallback()
        } else {
            // 不允许游客模式，显示登录框
            showLoginComponent(pageContext)
            if (onLoginRequired) {
                onLoginRequired()
            }
        }
    }

    // 处理登录状态变化
    function processLoginState(isLoggedIn) {
        if (hasProcessed) return // 防止重复处理

        hasProcessed = true
        if (isLoggedIn) {
            hideLoginComponent(pageContext)
            handleLoginSuccess()
        } else {
            handleNotLoggedIn()
        }
    }

    // 监听登录状态变化 - 实时响应
    const loginWatcher = app.watch('globalData.hasLogin', (newValue, oldValue) => {
        // 只有状态真正发生变化时才处理
        if (newValue !== oldValue) {
            hasProcessed = false // 重置处理标志
            processLoginState(newValue)
        }
    })

    // 初始状态检查 - 短暂延迟后执行
    const initialTimer = setTimeout(() => {
        processLoginState(app.globalData.hasLogin)
    }, initialDelay)

    // 返回控制方法
    return {
        // 清理方法，页面卸载时调用
        cleanup: () => {
            clearTimeout(initialTimer)
            if (loginWatcher) {
                loginWatcher()
            }
        },
        // 手动触发登录成功处理
        handleLoginSuccess: () => {
            hideLoginComponent(pageContext)
            handleLoginSuccess()
        },
        // 手动显示登录框
        showLogin: () => showLoginComponent(pageContext),
        // 手动隐藏登录框
        hideLogin: () => hideLoginComponent(pageContext),
        // 检查当前登录状态
        isLoggedIn: () => app.globalData.hasLogin,
        // 强制重新检查登录状态
        recheckLoginStatus: () => {
            hasProcessed = false
            processLoginState(app.globalData.hasLogin)
        }
    }
}

/**
 * 检查登录状态（用于UI交互前的预检查）
 * 注意：如果你的操作会调用API，建议直接调用API让request拦截器处理
 * 
 * 适用场景：
 * - 页面跳转前检查（避免无效跳转）
 * - 非API操作前检查（如分享、本地操作等）
 * - 需要即时反馈的UI操作
 * 
 * 不适用场景：
 * - 纯数据获取（应依赖request拦截器）
 * - API调用前检查（request拦截器会处理）
 * 
 * @param {Object} pageContext 页面上下文
 * @param {Function} callback 检查通过后的回调
 * @returns {boolean} 是否通过检查
 */
function ensureLogin(pageContext, callback) {
    const app = getApp()
    if (!app.globalData.hasLogin) {
        // 显示登录组件
        showLoginComponent(pageContext, true)
        return false
    }

    // 登录检查通过，执行回调
    if (typeof callback === 'function') {
        callback()
    }
    return true
}

/**
 * 检查小区认证状态（用于UI交互前的预检查）
 * 
 * 适用场景：
 * - 需要小区认证的功能操作前检查
 * - 页面跳转前检查小区认证状态
 * - 需要即时反馈的UI操作
 * 
 * @param {Object} pageContext 页面上下文
 * @param {Function} callback 检查通过后的回调
 * @returns {boolean} 是否通过检查
 */
function ensureResidentialAuth(pageContext, callback) {
    const app = getApp()
    if (!app.globalData.hasLogin) {
        // 未登录，显示登录组件
        showLoginComponent(pageContext, true)
        return false
    }

    // 检查用户信息是否存在
    const userInfo = app.globalData.userInfo
    if (!userInfo) {
        // 用户信息不存在，可能需要重新获取
        wx.showToast({
            title: '用户信息获取失败',
            icon: 'none',
            duration: 2000,
        })
        return false
    }
    console.log('userInfo', userInfo)

    // 检查小区认证状态
    if (userInfo.residentialCertificationStatus !== "1") {
        // 未认证，显示小区认证组件
        showResidentialAuthComponent(pageContext, true)
        return false
    }

    // 小区认证检查通过，执行回调
    if (typeof callback === 'function') {
        callback()
    }
    return true
}

/**
 * 显示小区认证组件
 * @param {Object} pageContext 页面上下文
 * @param {boolean} autoClose 认证成功后是否自动关闭
 * @returns {boolean} 是否成功显示小区认证组件
 */
function showResidentialAuthComponent(pageContext, autoClose = true) {
    if (!pageContext) return false

    const residentialAuthComp = pageContext.selectComponent('#residentialAuth')
    if (residentialAuthComp) {
        residentialAuthComp.setData({ autoClose: autoClose })
        residentialAuthComp.show()
        return true
    }

    // 如果找不到小区认证组件，显示提示
    wx.showToast({
        title: '请先完成小区认证',
        icon: 'none',
        duration: 2000,
    })
    return false
}

/**
 * 隐藏小区认证组件
 * @param {Object} pageContext 页面上下文
 * @returns {boolean} 是否成功隐藏小区认证组件
 */
function hideResidentialAuthComponent(pageContext) {
    if (!pageContext) return false

    const residentialAuthComp = pageContext.selectComponent('#residentialAuth')
    if (residentialAuthComp && residentialAuthComp.hide) {
        residentialAuthComp.hide()
        return true
    }
    return false
}

/**
 * 设置登录状态
 * @param {boolean} status 登录状态
 */
function setLoginStatus(status) {
    const app = getApp()
    if (app) {
        app.setLoginStatus(status)
    }
}


module.exports = {
    CONFIG,
    MESSAGES,
    ERROR_TYPES,
    checkSession,
    wxLogin,
    getDeviceInfo,
    silentLogin,
    updateUserInfo,
    checkLoginStatus,
    clearLoginState,
    handleLoginError,
    showLoginComponent,
    hideLoginComponent,
    ensureLogin,
    setLoginStatus,
    initPageLogin,
    isSilentLoginCompleted,
    resetSilentLoginState,
    ensureResidentialAuth,
    showResidentialAuthComponent,
    hideResidentialAuthComponent
}
